package com.charmshots.mobileapp.data

object SampleData {
    val languages = listOf(
        Language("en", "English", "🇺🇸"),
        Language("es", "Spanish", "🇪🇸"),
        Language("fr", "French", "🇫🇷"),
        Language("de", "German", "🇩🇪"),
        Language("it", "Italian", "🇮🇹")
    )

    val categories = listOf(
        Category("funny", "Funny", "Humorous and lighthearted pickup lines", "😄"),
        Category("romantic", "Romantic", "Sweet and romantic pickup lines", "💕"),
        Category("clever", "Clever", "Witty and intelligent pickup lines", "🧠"),
        Category("cheesy", "Cheesy", "Classic cheesy pickup lines", "🧀"),
        Category("flirty", "Flirty", "Playful and flirtatious pickup lines", "😉")
    )

    val pickupLines = mapOf(
        "en" to mapOf(
            "funny" to listOf(
                PickupLine("1", "Are you a magician? Because whenever I look at you, everyone else disappears.", "funny", "en", 4.2f),
                PickupLine("2", "Do you have a Band-Aid? Because I just scraped my knee falling for you.", "funny", "en", 3.8f),
                PickupLine("3", "Are you a parking ticket? Because you've got 'FINE' written all over you.", "funny", "en", 4.0f)
            ),
            "romantic" to listOf(
                PickupLine("4", "Do you believe in love at first sight, or should I walk by again?", "romantic", "en", 4.5f),
                PickupLine("5", "If I could rearrange the alphabet, I'd put U and I together.", "romantic", "en", 3.9f),
                PickupLine("6", "Are you a star? Because your beauty lights up the night.", "romantic", "en", 4.3f)
            ),
            "clever" to listOf(
                PickupLine("7", "Are you made of copper and tellurium? Because you're Cu-Te.", "clever", "en", 4.1f),
                PickupLine("8", "If you were a triangle, you'd be acute one.", "clever", "en", 3.7f),
                PickupLine("9", "Are you a 45-degree angle? Because you're acute-y.", "clever", "en", 3.6f)
            ),
            "cheesy" to listOf(
                PickupLine("10", "Do you have a map? I keep getting lost in your eyes.", "cheesy", "en", 3.5f),
                PickupLine("11", "Are you a camera? Because every time I look at you, I smile.", "cheesy", "en", 3.8f),
                PickupLine("12", "Is your name Google? Because you have everything I've been searching for.", "cheesy", "en", 3.9f)
            ),
            "flirty" to listOf(
                PickupLine("13", "Do you have a sunburn, or are you always this hot?", "flirty", "en", 3.4f),
                PickupLine("14", "Are you a loan from a bank? Because you have my interest.", "flirty", "en", 3.7f),
                PickupLine("15", "If looks could kill, you'd be a weapon of mass destruction.", "flirty", "en", 4.0f)
            )
        ),
        "es" to mapOf(
            "funny" to listOf(
                PickupLine("16", "¿Eres un mago? Porque cada vez que te miro, todos los demás desaparecen.", "funny", "es", 4.2f),
                PickupLine("17", "¿Tienes una curita? Porque me raspé la rodilla al caer por ti.", "funny", "es", 3.8f)
            ),
            "romantic" to listOf(
                PickupLine("18", "¿Crees en el amor a primera vista, o debo pasar otra vez?", "romantic", "es", 4.5f),
                PickupLine("19", "Si pudiera reorganizar el alfabeto, pondría la U y la I juntas.", "romantic", "es", 3.9f)
            ),
            "clever" to listOf(
                PickupLine("20", "¿Estás hecha de cobre y telurio? Porque eres Cu-Te.", "clever", "es", 4.1f)
            ),
            "cheesy" to listOf(
                PickupLine("21", "¿Tienes un mapa? Me sigo perdiendo en tus ojos.", "cheesy", "es", 3.5f)
            ),
            "flirty" to listOf(
                PickupLine("22", "¿Tienes quemadura de sol, o siempre estás así de ardiente?", "flirty", "es", 3.4f)
            )
        ),
        "fr" to mapOf(
            "funny" to listOf(
                PickupLine("23", "Es-tu un magicien? Car chaque fois que je te regarde, tout le monde disparaît.", "funny", "fr", 4.2f)
            ),
            "romantic" to listOf(
                PickupLine("24", "Crois-tu au coup de foudre, ou dois-je repasser?", "romantic", "fr", 4.5f)
            ),
            "clever" to listOf(
                PickupLine("25", "Es-tu faite de cuivre et de tellure? Parce que tu es Cu-Te.", "clever", "fr", 4.1f)
            ),
            "cheesy" to listOf(
                PickupLine("26", "As-tu une carte? Je me perds dans tes yeux.", "cheesy", "fr", 3.5f)
            ),
            "flirty" to listOf(
                PickupLine("27", "As-tu un coup de soleil, ou es-tu toujours aussi chaude?", "flirty", "fr", 3.4f)
            )
        )
    )

    fun getPickupLinesForCategory(language: String, category: String): List<PickupLine> {
        return pickupLines[language]?.get(category) ?: emptyList()
    }
}

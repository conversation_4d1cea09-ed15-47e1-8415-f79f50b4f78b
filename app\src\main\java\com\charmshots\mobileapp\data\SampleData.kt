package com.charmshots.mobileapp.data

object SampleData {
    val languages = listOf(
        Language("hi", "Hindi", "🇮🇳"),
        Language("en", "English", "🇬🇧")
    )

    val categories = listOf(
        Category("romantic", "Romantic", "Sweet and romantic lines", "💕"),
        Category("flirty", "Flirty", "Playful and flirtatious lines", "😉"),
        Category("cute", "Cute", "Adorable and sweet lines", "🥰"),
        Category("funny", "Funny", "Humorous and lighthearted lines", "😄"),
        Category("bad", "Bad", "Naughty and bold lines", "😈"),
        Category("clever", "Clever", "Witty and intelligent lines", "🧠"),
        Category("genius", "Genius", "Brilliant and creative lines", "🎯")
    )

    val pickupLines = mapOf(
        "en" to mapOf(
            "romantic" to listOf(
                PickupLine("1", "Do you believe in love at first sight, or should I walk by again?", "romantic", "en", 4.5f),
                PickupLine("2", "If I could rearrange the alphabet, I'd put <PERSON> and I together.", "romantic", "en", 3.9f),
                PickupLine("3", "Are you a star? Because your beauty lights up the night.", "romantic", "en", 4.3f)
            ),
            "flirty" to listOf(
                PickupLine("4", "Are you a parking ticket? Because you've got 'FINE' written all over you.", "flirty", "en", 4.0f),
                PickupLine("5", "Do you have a map? I keep getting lost in your eyes.", "flirty", "en", 4.1f),
                PickupLine("6", "Is your name Google? Because you have everything I've been searching for.", "flirty", "en", 3.8f)
            ),
            "cute" to listOf(
                PickupLine("7", "Are you a 45-degree angle? Because you're acute!", "cute", "en", 4.2f),
                PickupLine("8", "Do you have a Band-Aid? Because I just scraped my knee falling for you.", "cute", "en", 3.8f),
                PickupLine("9", "Are you made of copper and tellurium? Because you're Cu-Te!", "cute", "en", 4.0f)
            ),
            "funny" to listOf(
                PickupLine("10", "Are you a magician? Because whenever I look at you, everyone else disappears.", "funny", "en", 4.2f),
                PickupLine("11", "Do you work at Starbucks? Because I like you a latte.", "funny", "en", 3.7f),
                PickupLine("12", "Are you WiFi? Because I'm feeling a connection.", "funny", "en", 3.9f)
            ),
            "bad" to listOf(
                PickupLine("13", "Are you my homework? Because I should be doing you on my desk.", "bad", "en", 3.5f),
                PickupLine("14", "Do you work at a construction site? Because you're building.", "bad", "en", 3.8f),
                PickupLine("15", "Are you a parking ticket? Because you've got trouble written all over you.", "bad", "en", 3.7f)
            ),
            "clever" to listOf(
                PickupLine("16", "Are you made of copper and tellurium? Because you're Cu-Te.", "clever", "en", 4.1f),
                PickupLine("17", "If you were a triangle, you'd be acute one.", "clever", "en", 3.7f),
                PickupLine("18", "Are you a 90-degree angle? Because you're looking right.", "clever", "en", 3.6f)
            ),
            "genius" to listOf(
                PickupLine("19", "Are you the square root of -1? Because you can't be real.", "genius", "en", 4.3f),
                PickupLine("20", "You must be the speed of light because time stops when I see you.", "genius", "en", 4.2f),
                PickupLine("21", "Are you a carbon sample? Because I want to date you.", "genius", "en", 4.0f)
            )
        ),
        "hi" to mapOf(
            "romantic" to listOf(
                PickupLine("22", "क्या तुम चाँद हो? क्योंकि तुम्हारी रोशनी से मेरी रात जगमगा जाती है।", "romantic", "hi", 4.5f),
                PickupLine("23", "अगर प्यार एक अपराध है, तो मैं तुम्हारे लिए जेल जाने को तैयार हूँ।", "romantic", "hi", 4.3f),
                PickupLine("24", "तुम्हारी आँखों में वो जादू है जो मुझे तुम्हारा दीवाना बना देता है।", "romantic", "hi", 4.4f)
            ),
            "flirty" to listOf(
                PickupLine("25", "क्या तुम WiFi हो? क्योंकि मैं तुमसे connection feel कर रहा हूँ।", "flirty", "hi", 4.0f),
                PickupLine("26", "तुम इतनी hot हो कि मेरा phone भी overheat हो जाता है।", "flirty", "hi", 3.8f),
                PickupLine("27", "क्या तुम Google हो? क्योंकि तुममें वो सब कुछ है जो मैं ढूंढ रहा था।", "flirty", "hi", 3.9f)
            ),
            "cute" to listOf(
                PickupLine("28", "तुम इतनी मीठी हो कि चीनी भी शर्मा जाए!", "cute", "hi", 4.2f),
                PickupLine("29", "क्या तुम 45-degree angle हो? क्योंकि तुम cutie हो!", "cute", "hi", 4.0f),
                PickupLine("30", "तुम्हारी smile देखकर मेरा दिल भी smile करने लगता है।", "cute", "hi", 4.1f)
            ),
            "funny" to listOf(
                PickupLine("31", "क्या तुम magician हो? क्योंकि जब भी मैं तुम्हें देखता हूँ, बाकी सब गायब हो जाते हैं।", "funny", "hi", 4.2f),
                PickupLine("32", "क्या तुम्हारे पास Band-Aid है? क्योंकि तुम्हारे प्यार में गिरकर मुझे चोट लग गई।", "funny", "hi", 3.8f),
                PickupLine("33", "क्या तुम parking ticket हो? क्योंकि तुम पर 'FINE' लिखा है।", "funny", "hi", 4.0f)
            ),
            "bad" to listOf(
                PickupLine("34", "क्या तुम homework हो? क्योंकि मुझे तुम्हें अपनी table पर करना चाहिए।", "bad", "hi", 3.5f),
                PickupLine("35", "तुम इतनी hot हो कि AC भी fail हो जाए।", "bad", "hi", 3.7f),
                PickupLine("36", "क्या तुम fire alarm हो? क्योंकि तुम बहुत loud और hot हो।", "bad", "hi", 3.6f)
            ),
            "clever" to listOf(
                PickupLine("37", "क्या तुम copper और tellurium से बनी हो? क्योंकि तुम Cu-Te हो।", "clever", "hi", 4.1f),
                PickupLine("38", "अगर तुम triangle होती तो acute होती।", "clever", "hi", 3.7f),
                PickupLine("39", "क्या तुम 90-degree angle हो? क्योंकि तुम बिल्कुल right लग रही हो।", "clever", "hi", 3.8f)
            ),
            "genius" to listOf(
                PickupLine("40", "क्या तुम square root of -1 हो? क्योंकि तुम real नहीं लग सकती।", "genius", "hi", 4.3f),
                PickupLine("41", "तुम light की speed होगी क्योंकि तुम्हें देखकर time रुक जाता है।", "genius", "hi", 4.2f),
                PickupLine("42", "क्या तुम carbon sample हो? क्योंकि मैं तुम्हें date करना चाहता हूँ।", "genius", "hi", 4.0f)
            )
        )
    )

    fun getPickupLinesForCategory(language: String, category: String): List<PickupLine> {
        return pickupLines[language]?.get(category) ?: emptyList()
    }
}

package com.charmshots.mobileapp.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

data class DrawerMenuItem(
    val title: String,
    val icon: ImageVector,
    val subtitle: String? = null,
    val onClick: () -> Unit
)

@Composable
fun DrawerContent(
    onMenuItemClick: (String) -> Unit,
    onCloseDrawer: () -> Unit
) {
    val menuItems = listOf(
        DrawerMenuItem(
            title = "Today's Shots",
            icon = Icons.Default.DateRange,
            onClick = { onMenuItemClick("today_shots") }
        ),
        DrawerMenuItem(
            title = "Favourite (0)",
            icon = Icons.Default.Favorite,
            onClick = { onMenuItemClick("favourite") }
        ),
        DrawerMenuItem(
            title = "Shots Maker",
            icon = Icons.Default.Edit,
            onClick = { onMenuItemClick("shots_maker") }
        ),
        DrawerMenuItem(
            title = "Settings",
            icon = Icons.Default.Settings,
            onClick = { onMenuItemClick("settings") }
        ),
        DrawerMenuItem(
            title = "Privacy Policy",
            icon = Icons.Default.Info,
            onClick = { onMenuItemClick("privacy_policy") }
        ),
        DrawerMenuItem(
            title = "Terms and Conditions",
            icon = Icons.Default.Description,
            onClick = { onMenuItemClick("terms_conditions") }
        ),
        DrawerMenuItem(
            title = "Rate Us",
            icon = Icons.Default.Star,
            onClick = { onMenuItemClick("rate_us") }
        ),
        DrawerMenuItem(
            title = "Share App",
            icon = Icons.Default.Share,
            onClick = { onMenuItemClick("share_app") }
        ),
        DrawerMenuItem(
            title = "Performance Debug",
            icon = Icons.Default.BugReport,
            subtitle = "Debug performance issues",
            onClick = { onMenuItemClick("performance_debug") }
        )
    )

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(16.dp)
    ) {
        // Header with app logo and title
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 24.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // App icon placeholder (you can replace with actual image)
            Box(
                modifier = Modifier
                    .size(60.dp)
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.primary),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "👫",
                    fontSize = 30.sp
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Text(
                text = "Charm Shots",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = Color.Black
            )
        }

        Divider(
            modifier = Modifier.padding(vertical = 8.dp),
            color = Color.Gray.copy(alpha = 0.3f)
        )

        // Menu items
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            items(menuItems) { item ->
                DrawerMenuItemRow(
                    item = item,
                    onClick = {
                        item.onClick()
                        onCloseDrawer()
                    }
                )
            }
        }

        Spacer(modifier = Modifier.weight(1f))

        // Version info at bottom
        Text(
            text = "v1.0.0",
            style = MaterialTheme.typography.bodySmall,
            color = Color.Gray,
            modifier = Modifier.align(Alignment.CenterHorizontally)
        )
    }
}

@Composable
fun DrawerMenuItemRow(
    item: DrawerMenuItem,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 12.dp, horizontal = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = item.icon,
            contentDescription = item.title,
            tint = Color.Black,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = item.title,
                style = MaterialTheme.typography.bodyLarge,
                color = Color.Black,
                fontWeight = FontWeight.Medium
            )
            
            item.subtitle?.let { subtitle ->
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.Gray
                )
            }
        }
    }
}

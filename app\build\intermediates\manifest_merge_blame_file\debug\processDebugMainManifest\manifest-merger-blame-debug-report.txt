1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.charmshots.mobileapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="36" />
10
11    <permission
11-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9ceb9d53329fb14966459c5fe2fde6a\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.charmshots.mobileapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9ceb9d53329fb14966459c5fe2fde6a\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9ceb9d53329fb14966459c5fe2fde6a\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.charmshots.mobileapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9ceb9d53329fb14966459c5fe2fde6a\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9ceb9d53329fb14966459c5fe2fde6a\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:5:5-25:19
18        android:allowBackup="true"
18-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9ceb9d53329fb14966459c5fe2fde6a\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:12:9-35
28        android:theme="@style/Theme.MyApplication" >
28-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:13:9-51
29        <activity
29-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:14:9-24:20
30            android:name="com.charmshots.mobileapp.MainActivity"
30-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:15:13-41
31            android:exported="true"
31-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:16:13-36
32            android:label="@string/app_name"
32-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:17:13-45
33            android:theme="@style/Theme.MyApplication" >
33-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:18:13-55
34            <intent-filter>
34-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:19:13-23:29
35                <action android:name="android.intent.action.MAIN" />
35-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:20:17-69
35-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:20:25-66
36
37                <category android:name="android.intent.category.LAUNCHER" />
37-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:22:17-77
37-->C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\AndroidManifest.xml:22:27-74
38            </intent-filter>
39        </activity>
40        <activity
40-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2586597ec651bc668e6bb3009a1b2e37\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
41            android:name="androidx.compose.ui.tooling.PreviewActivity"
41-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2586597ec651bc668e6bb3009a1b2e37\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
42            android:exported="true" />
42-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2586597ec651bc668e6bb3009a1b2e37\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
43        <activity
43-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b62cb5a9f546252a0a3797cbdd9aeee7\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
44            android:name="androidx.activity.ComponentActivity"
44-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b62cb5a9f546252a0a3797cbdd9aeee7\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
45            android:exported="true" />
45-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b62cb5a9f546252a0a3797cbdd9aeee7\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
46
47        <provider
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1491772b01dd1e16f4656bb47cf00ff\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
48            android:name="androidx.startup.InitializationProvider"
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1491772b01dd1e16f4656bb47cf00ff\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
49            android:authorities="com.charmshots.mobileapp.androidx-startup"
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1491772b01dd1e16f4656bb47cf00ff\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
50            android:exported="false" >
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1491772b01dd1e16f4656bb47cf00ff\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
51            <meta-data
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1491772b01dd1e16f4656bb47cf00ff\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
52                android:name="androidx.emoji2.text.EmojiCompatInitializer"
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1491772b01dd1e16f4656bb47cf00ff\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
53                android:value="androidx.startup" />
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1491772b01dd1e16f4656bb47cf00ff\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
54            <meta-data
54-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\793e1aa12338050289d5cce55361ad46\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
55                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
55-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\793e1aa12338050289d5cce55361ad46\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
56                android:value="androidx.startup" />
56-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\793e1aa12338050289d5cce55361ad46\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
57            <meta-data
57-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
58-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
59                android:value="androidx.startup" />
59-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
60        </provider>
61
62        <receiver
62-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
63            android:name="androidx.profileinstaller.ProfileInstallReceiver"
63-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
64            android:directBootAware="false"
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
65            android:enabled="true"
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
66            android:exported="true"
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
67            android:permission="android.permission.DUMP" >
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
68            <intent-filter>
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
69                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
70            </intent-filter>
71            <intent-filter>
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
72                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
73            </intent-filter>
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
75                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
78                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
79            </intent-filter>
80        </receiver>
81    </application>
82
83</manifest>

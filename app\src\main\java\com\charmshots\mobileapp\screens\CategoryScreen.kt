package com.charmshots.mobileapp.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.charmshots.mobileapp.data.Category
import com.charmshots.mobileapp.data.Language
import com.charmshots.mobileapp.data.SampleData

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CategoryScreen(
    language: Language,
    onCategorySelected: (Category) -> Unit,
    onBackPressed: () -> Unit,
    onOpenDrawer: () -> Unit = {}
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Charm Shots",
                        fontWeight = FontWeight.Bold,
                        color = Color.Black
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onOpenDrawer) {
                        Icon(
                            imageVector = Icons.Default.Menu,
                            contentDescription = "Menu",
                            tint = Color.Black
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            contentPadding = PaddingValues(vertical = 16.dp)
        ) {
            items(SampleData.categories) { category ->
                CategoryCard(
                    category = category,
                    onClick = { onCategorySelected(category) }
                )
            }
        }
    }
}

@Composable
fun CategoryCard(
    category: Category,
    onClick: () -> Unit
) {
    val gradient = when (category.id) {
        "romantic" -> Brush.horizontalGradient(
            colors = listOf(Color(0xFF87CEEB), Color(0xFFFFB6C1))
        )
        "flirty" -> Brush.horizontalGradient(
            colors = listOf(Color(0xFFFF69B4), Color(0xFFFFB6C1))
        )
        "cute" -> Brush.horizontalGradient(
            colors = listOf(Color(0xFF7B68EE), Color(0xFF20B2AA))
        )
        "funny" -> Brush.horizontalGradient(
            colors = listOf(Color(0xFF90EE90), Color(0xFFFFFF00))
        )
        "bad" -> Brush.horizontalGradient(
            colors = listOf(Color(0xFF4B0082), Color(0xFF8A2BE2))
        )
        "clever" -> Brush.horizontalGradient(
            colors = listOf(Color(0xFF4B0082), Color(0xFF8A2BE2))
        )
        "genius" -> Brush.horizontalGradient(
            colors = listOf(Color(0xFF20B2AA), Color(0xFF4682B4))
        )
        else -> Brush.horizontalGradient(
            colors = listOf(Color(0xFF7B68EE), Color(0xFFDA70D6))
        )
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(80.dp)
            .clip(RoundedCornerShape(20.dp))
            .background(gradient)
            .clickable { onClick() }
            .padding(horizontal = 20.dp, vertical = 16.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxSize(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Icon background
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .clip(RoundedCornerShape(12.dp))
                        .background(Color.White.copy(alpha = 0.2f)),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = category.icon,
                        fontSize = 24.sp
                    )
                }

                Text(
                    text = category.name,
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            }

            Icon(
                imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                contentDescription = "Arrow",
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

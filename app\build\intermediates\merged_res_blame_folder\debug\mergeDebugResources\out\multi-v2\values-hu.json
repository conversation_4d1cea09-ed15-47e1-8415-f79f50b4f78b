{"logs": [{"outputFile": "com.charmshots.mobileapp-mergeDebugResources-52:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b9ceb9d53329fb14966459c5fe2fde6a\\transformed\\core-1.16.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,406,507,610,717,8438", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "197,299,401,502,605,712,822,8534"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d535297dfa84f089b23885eaaed96503\\transformed\\ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,288,385,484,571,653,749,838,925,1008,1096,1170,1245,1316,1386,1465,1531", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,74,70,69,78,65,120", "endOffsets": "195,283,380,479,566,648,744,833,920,1003,1091,1165,1240,1311,1381,1460,1526,1647"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "827,922,1010,1107,1206,1293,1375,7801,7890,7977,8060,8148,8222,8297,8368,8539,8618,8684", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,74,70,69,78,65,120", "endOffsets": "917,1005,1102,1201,1288,1370,1466,7885,7972,8055,8143,8217,8292,8363,8433,8613,8679,8800"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f1b3ebe766370ce6491c63648b9277cf\\transformed\\material3-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,292,400,516,611,708,822,962,1085,1232,1317,1417,1515,1617,1739,1876,1981,2121,2259,2385,2581,2704,2826,2948,3074,3173,3268,3387,3524,3626,3737,3841,3986,4133,4240,4347,4431,4529,4623,4731,4819,4906,5007,5088,5171,5270,5376,5471,5574,5660,5769,5867,5973,6094,6175,6287", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "171,287,395,511,606,703,817,957,1080,1227,1312,1412,1510,1612,1734,1871,1976,2116,2254,2380,2576,2699,2821,2943,3069,3168,3263,3382,3519,3621,3732,3836,3981,4128,4235,4342,4426,4524,4618,4726,4814,4901,5002,5083,5166,5265,5371,5466,5569,5655,5764,5862,5968,6089,6170,6282,6380"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1471,1592,1708,1816,1932,2027,2124,2238,2378,2501,2648,2733,2833,2931,3033,3155,3292,3397,3537,3675,3801,3997,4120,4242,4364,4490,4589,4684,4803,4940,5042,5153,5257,5402,5549,5656,5763,5847,5945,6039,6147,6235,6322,6423,6504,6587,6686,6792,6887,6990,7076,7185,7283,7389,7510,7591,7703", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "1587,1703,1811,1927,2022,2119,2233,2373,2496,2643,2728,2828,2926,3028,3150,3287,3392,3532,3670,3796,3992,4115,4237,4359,4485,4584,4679,4798,4935,5037,5148,5252,5397,5544,5651,5758,5842,5940,6034,6142,6230,6317,6418,6499,6582,6681,6787,6882,6985,7071,7180,7278,7384,7505,7586,7698,7796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8886a8810f651f0db96100876e45c5f4\\transformed\\foundation-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,96", "endOffsets": "139,236"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8805,8894", "endColumns": "88,96", "endOffsets": "8889,8986"}}]}]}
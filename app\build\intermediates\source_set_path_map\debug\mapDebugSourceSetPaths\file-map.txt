com.charmshots.mobileapp-navigation-runtime-2.7.5-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\01244875b301a5a792c2ef1d0eb996bc\transformed\navigation-runtime-2.7.5\res
com.charmshots.mobileapp-core-ktx-1.16.0-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\02f19c8dc9a99d40208a9084515efdeb\transformed\core-ktx-1.16.0\res
com.charmshots.mobileapp-runtime-release-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\051016c11c1f70663f0b31fb6b642183\transformed\runtime-release\res
com.charmshots.mobileapp-material-ripple-release-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\055a826dce14c3082f9ff9cb79ccad78\transformed\material-ripple-release\res
com.charmshots.mobileapp-ui-util-release-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\09011ebae7703987b7f75f4ffe51ab04\transformed\ui-util-release\res
com.charmshots.mobileapp-ui-geometry-release-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\0ef77c50a3d9d8b5c73954b2dd93a331\transformed\ui-geometry-release\res
com.charmshots.mobileapp-navigation-common-2.7.5-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\12db27b9d80dcbb273c79c91975aa509\transformed\navigation-common-2.7.5\res
com.charmshots.mobileapp-activity-compose-1.8.2-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\1481ab4a6de9acf7f12d6937fc06fabb\transformed\activity-compose-1.8.2\res
com.charmshots.mobileapp-lifecycle-runtime-compose-release-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\16bcbd7586f4feaa06c28dbd6eba348f\transformed\lifecycle-runtime-compose-release\res
com.charmshots.mobileapp-activity-1.8.2-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\1d9c19a79102edc2b178e677fc81ba22\transformed\activity-1.8.2\res
com.charmshots.mobileapp-graphics-path-1.0.1-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\218c4dfbc5426d9b7ae5bf9992e9ea6b\transformed\graphics-path-1.0.1\res
com.charmshots.mobileapp-material-icons-core-release-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\238f0f989de52f7a9d35ff6374097201\transformed\material-icons-core-release\res
com.charmshots.mobileapp-ui-tooling-data-release-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\2517d3a50668cfbdaccd026e35918ba3\transformed\ui-tooling-data-release\res
com.charmshots.mobileapp-ui-tooling-release-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\2586597ec651bc668e6bb3009a1b2e37\transformed\ui-tooling-release\res
com.charmshots.mobileapp-ui-graphics-release-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\2860045c1ed88db8020c16b358c2645f\transformed\ui-graphics-release\res
com.charmshots.mobileapp-navigation-common-ktx-2.7.5-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\2ca9c36a702301580a0b916cfbc996a4\transformed\navigation-common-ktx-2.7.5\res
com.charmshots.mobileapp-lifecycle-runtime-release-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\2cd8e528669426f987e803805efaf5ff\transformed\lifecycle-runtime-release\res
com.charmshots.mobileapp-lifecycle-livedata-core-2.8.3-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb51b71c138d9cf0e7f3b976ea56114\transformed\lifecycle-livedata-core-2.8.3\res
com.charmshots.mobileapp-savedstate-ktx-1.2.1-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\3289f31c776b7959c7478f53a26f084f\transformed\savedstate-ktx-1.2.1\res
com.charmshots.mobileapp-profileinstaller-1.3.1-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\3808e1cf3bb87cafe134c9ad3fbd724b\transformed\profileinstaller-1.3.1\res
com.charmshots.mobileapp-activity-ktx-1.8.2-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\3e4c5f1361b44f1dbe2519b3e98e172a\transformed\activity-ktx-1.8.2\res
com.charmshots.mobileapp-animation-release-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\51b6945a952c63fdcb35cf8b1019b9dd\transformed\animation-release\res
com.charmshots.mobileapp-core-runtime-2.2.0-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\54dd4dfa2b848a6107c76ac46e57140b\transformed\core-runtime-2.2.0\res
com.charmshots.mobileapp-runtime-saveable-release-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\5aae295e63e0b03a56c8a2d93080b382\transformed\runtime-saveable-release\res
com.charmshots.mobileapp-lifecycle-viewmodel-compose-release-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\5b2064fc7607d039ed7d0e5262f9171c\transformed\lifecycle-viewmodel-compose-release\res
com.charmshots.mobileapp-annotation-experimental-1.4.1-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\66a96bec4229c519986788697e99846a\transformed\annotation-experimental-1.4.1\res
com.charmshots.mobileapp-navigation-runtime-ktx-2.7.5-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\69843bcb5a605e664128231415b97c6e\transformed\navigation-runtime-ktx-2.7.5\res
com.charmshots.mobileapp-lifecycle-process-2.8.3-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\793e1aa12338050289d5cce55361ad46\transformed\lifecycle-process-2.8.3\res
com.charmshots.mobileapp-core-viewtree-1.0.0-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\82153733e2f6c3ff2b2f927e7ab5021e\transformed\core-viewtree-1.0.0\res
com.charmshots.mobileapp-foundation-release-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\8886a8810f651f0db96100876e45c5f4\transformed\foundation-release\res
com.charmshots.mobileapp-startup-runtime-1.1.1-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\8d0dcaf87db2277dc6c1b01601957026\transformed\startup-runtime-1.1.1\res
com.charmshots.mobileapp-lifecycle-viewmodel-savedstate-2.8.3-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\8d3704ae6a8db234fcdebef519deea5b\transformed\lifecycle-viewmodel-savedstate-2.8.3\res
com.charmshots.mobileapp-material-release-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\8e690fa278d9df06758eb8ed3883655b\transformed\material-release\res
com.charmshots.mobileapp-lifecycle-runtime-ktx-release-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\93538f61d7565e3c63c2ed8c84b74ac7\transformed\lifecycle-runtime-ktx-release\res
com.charmshots.mobileapp-animation-core-release-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\9f6f8af56c69d0b5ec1d5bd646590926\transformed\animation-core-release\res
com.charmshots.mobileapp-emoji2-1.3.0-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\a1491772b01dd1e16f4656bb47cf00ff\transformed\emoji2-1.3.0\res
com.charmshots.mobileapp-navigation-compose-2.7.5-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\a97f4d7502eed406a5f48f7d62e26477\transformed\navigation-compose-2.7.5\res
com.charmshots.mobileapp-foundation-layout-release-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\b2e518d6ef8709b958ef8d3217aaecdd\transformed\foundation-layout-release\res
com.charmshots.mobileapp-ui-test-manifest-1.7.0-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\b62cb5a9f546252a0a3797cbdd9aeee7\transformed\ui-test-manifest-1.7.0\res
com.charmshots.mobileapp-core-1.16.0-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\b9ceb9d53329fb14966459c5fe2fde6a\transformed\core-1.16.0\res
com.charmshots.mobileapp-ui-text-release-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\bcfa68de351102819d6c90f051342810\transformed\ui-text-release\res
com.charmshots.mobileapp-tracing-1.2.0-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\cddbefac1dccb6b2c3ae795b4748d5ff\transformed\tracing-1.2.0\res
com.charmshots.mobileapp-lifecycle-viewmodel-ktx-2.8.3-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\d1007200a621822e6dddfca254f669c6\transformed\lifecycle-viewmodel-ktx-2.8.3\res
com.charmshots.mobileapp-ui-unit-release-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\d4da4503b2d675b667960f510958186c\transformed\ui-unit-release\res
com.charmshots.mobileapp-ui-release-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\d535297dfa84f089b23885eaaed96503\transformed\ui-release\res
com.charmshots.mobileapp-customview-poolingcontainer-1.0.0-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea359641f107f105976d0e6e6082b\transformed\customview-poolingcontainer-1.0.0\res
com.charmshots.mobileapp-savedstate-1.2.1-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\ee4cf2ae59c5ebf4338b65bd7f875d00\transformed\savedstate-1.2.1\res
com.charmshots.mobileapp-lifecycle-viewmodel-release-47 C:\Users\<USER>\.gradle\caches\8.13\transforms\efb66edaecdb1fadc22ad434d2abc383\transformed\lifecycle-viewmodel-release\res
com.charmshots.mobileapp-material3-release-48 C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b3ebe766370ce6491c63648b9277cf\transformed\material3-release\res
com.charmshots.mobileapp-ui-tooling-preview-release-49 C:\Users\<USER>\.gradle\caches\8.13\transforms\f8bc7c0e54e100bbc2f16e8efaa12c62\transformed\ui-tooling-preview-release\res
com.charmshots.mobileapp-pngs-50 C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\build\generated\res\pngs\debug
com.charmshots.mobileapp-resValues-51 C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\build\generated\res\resValues\debug
com.charmshots.mobileapp-packageDebugResources-52 C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.charmshots.mobileapp-packageDebugResources-53 C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.charmshots.mobileapp-debug-54 C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\build\intermediates\merged_res\debug\mergeDebugResources
com.charmshots.mobileapp-debug-55 C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\debug\res
com.charmshots.mobileapp-main-56 C:\Users\<USER>\OneDrive\Desktop\Newfolder\app\src\main\res

package com.charmshots.mobileapp.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.charmshots.mobileapp.data.SampleData
import com.charmshots.mobileapp.screens.CategoryScreen
import com.charmshots.mobileapp.screens.LanguageSelectionScreen
import com.charmshots.mobileapp.screens.PickupLineListScreen

sealed class Screen(val route: String) {
    object LanguageSelection : Screen("language_selection")
    object Category : Screen("category/{languageCode}") {
        fun createRoute(languageCode: String) = "category/$languageCode"
    }
    object PickupLineList : Screen("pickup_lines/{languageCode}/{categoryId}") {
        fun createRoute(languageCode: String, categoryId: String) = "pickup_lines/$languageCode/$categoryId"
    }
}

@Composable
fun AppNavigation(
    navController: NavHostController = rememberNavController()
) {
    NavHost(
        navController = navController,
        startDestination = Screen.LanguageSelection.route
    ) {
        composable(Screen.LanguageSelection.route) {
            LanguageSelectionScreen(
                onLanguageSelected = { language ->
                    navController.navigate(Screen.Category.createRoute(language.code))
                }
            )
        }
        
        composable(Screen.Category.route) { backStackEntry ->
            val languageCode = backStackEntry.arguments?.getString("languageCode") ?: "en"
            val language = SampleData.languages.find { it.code == languageCode }
                ?: SampleData.languages.first()
            
            CategoryScreen(
                language = language,
                onCategorySelected = { category ->
                    navController.navigate(
                        Screen.PickupLineList.createRoute(language.code, category.id)
                    )
                },
                onBackPressed = {
                    navController.popBackStack()
                }
            )
        }
        
        composable(Screen.PickupLineList.route) { backStackEntry ->
            val languageCode = backStackEntry.arguments?.getString("languageCode") ?: "en"
            val categoryId = backStackEntry.arguments?.getString("categoryId") ?: "funny"
            
            val language = SampleData.languages.find { it.code == languageCode }
                ?: SampleData.languages.first()
            val category = SampleData.categories.find { it.id == categoryId }
                ?: SampleData.categories.first()
            
            PickupLineListScreen(
                language = language,
                category = category,
                onBackPressed = {
                    navController.popBackStack()
                }
            )
        }
    }
}

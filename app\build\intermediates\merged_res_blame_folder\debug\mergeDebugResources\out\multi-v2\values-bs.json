{"logs": [{"outputFile": "com.charmshots.mobileapp-mergeDebugResources-52:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f1b3ebe766370ce6491c63648b9277cf\\transformed\\material3-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,415,535,634,732,847,992,1112,1250,1335,1435,1528,1626,1743,1870,1975,2110,2244,2385,2555,2690,2813,2940,3068,3162,3260,3381,3509,3606,3709,3818,3957,4102,4211,4311,4396,4489,4584,4711,4805,4896,5005,5093,5176,5273,5377,5470,5567,5655,5763,5860,5962,6100,6190,6298", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,126,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "169,289,410,530,629,727,842,987,1107,1245,1330,1430,1523,1621,1738,1865,1970,2105,2239,2380,2550,2685,2808,2935,3063,3157,3255,3376,3504,3601,3704,3813,3952,4097,4206,4306,4391,4484,4579,4706,4800,4891,5000,5088,5171,5268,5372,5465,5562,5650,5758,5855,5957,6095,6185,6293,6392"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1467,1586,1706,1827,1947,2046,2144,2259,2404,2524,2662,2747,2847,2940,3038,3155,3282,3387,3522,3656,3797,3967,4102,4225,4352,4480,4574,4672,4793,4921,5018,5121,5230,5369,5514,5623,5723,5808,5901,5996,6123,6217,6308,6417,6505,6588,6685,6789,6882,6979,7067,7175,7272,7374,7512,7602,7710", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,126,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "1581,1701,1822,1942,2041,2139,2254,2399,2519,2657,2742,2842,2935,3033,3150,3277,3382,3517,3651,3792,3962,4097,4220,4347,4475,4569,4667,4788,4916,5013,5116,5225,5364,5509,5618,5718,5803,5896,5991,6118,6212,6303,6412,6500,6583,6680,6784,6877,6974,7062,7170,7267,7369,7507,7597,7705,7804"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8886a8810f651f0db96100876e45c5f4\\transformed\\foundation-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,91", "endOffsets": "140,232"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8828,8918", "endColumns": "89,91", "endOffsets": "8913,9005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d535297dfa84f089b23885eaaed96503\\transformed\\ui-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,206,294,388,487,573,650,742,834,919,1000,1086,1159,1236,1315,1392,1472,1542", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,76,78,76,79,69,117", "endOffsets": "201,289,383,482,568,645,737,829,914,995,1081,1154,1231,1310,1387,1467,1537,1655"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "830,931,1019,1113,1212,1298,1375,7809,7901,7986,8067,8153,8226,8303,8382,8560,8640,8710", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,76,78,76,79,69,117", "endOffsets": "926,1014,1108,1207,1293,1370,1462,7896,7981,8062,8148,8221,8298,8377,8454,8635,8705,8823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b9ceb9d53329fb14966459c5fe2fde6a\\transformed\\core-1.16.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,403,507,611,713,8459", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "198,300,398,502,606,708,825,8555"}}]}]}